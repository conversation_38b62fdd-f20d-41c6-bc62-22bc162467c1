import { GamePlatformAdapter, ManagingPlatformAdapter, TransferJobData } from './models/model';
import {
    featureFlagValue,
    JobData,
    JobDataHandler,
    JobType,
    logging,
    LoginError,
    MttUserData,
    PlatformUserData,
    UserData
} from 'shared';
import redis from './redis';

export class PlatformAwareGameEntrypoint {
    private platformAdapter: ManagingPlatformAdapter;
    private gameAdapter: GamePlatformAdapter;

    constructor(platformAdapter: ManagingPlatformAdapter, gameAdapter: GamePlatformAdapter) {
        this.platformAdapter = platformAdapter;
        this.gameAdapter = gameAdapter;
    }

    async init(): Promise<void> {
        this.platformAdapter.init();
        await this.gameAdapter.init();
    }

    async start(
        user: UserData,
        action: JobType,
        tableId: number,
        params: JobData,
        onMessage: JobDataHandler,
    ): Promise<void> {
        try {
            switch (action) {
                case JobType.CHECK: {
                    const userData = await this.resolveUserData(user);
                    await this.gameAdapter.check(userData, tableId, onMessage);
                    break;
                }
                case JobType.SCAN: {
                    const userData = await this.resolveUserData(user);
                    await this.gameAdapter.scan(userData, params, onMessage);
                    break;
                }
                case JobType.PLAY: {
                    const userData = await this.resolveUserData(user);
                    await this.gameAdapter.play(userData, tableId, params, onMessage);
                    break;
                }
                case JobType.TRANSFER: {
                    const platformUser = await this.ensurePlatformUser(user);
                    await this.platformAdapter.transfer(platformUser, params as TransferJobData);
                    break;
                }
                case JobType.BALANCE: {
                    const platformUser = await this.ensurePlatformUser(user);
                    const balance = await this.platformAdapter.balance(platformUser);
                    if (this.gameAdapter.isMtt() && (await this.isFetchTickets())) {
                        const userData = await this.resolveUserData(user);
                        balance.tickets = await this.gameAdapter.balance(userData);
                    }
                    onMessage({ balance });
                    break;
                }
                default:
                    return Promise.reject(new Error(`Unsupported action: ${action}`));
            }
        } catch (e) {
            logging.error(`Error during action '${action}' for userId ${user.userId}`, e);
            if (e instanceof LoginError) {
                logging.info(`Login error detected, clearing login cache for userId ${user.userId}`);
                await this.clearCache(user);
            }
            throw e;
        }
    }

    async isFetchTickets() {
        return featureFlagValue('ticket-fetching-enabled', {}, false);
    }

    async stop(action: JobType): Promise<void> {
        await this.gameAdapter.stop(action);
    }

    private cacheKey(type: 'user' | 'mtt-user', user: UserData): string {
        return `${type}:${user.platformId}:${user.userId}`;
    }

    private async useCache(): Promise<boolean> {
        return featureFlagValue('user-cache-enabled', {}, true);
    }

    private async ensurePlatformUser(user: UserData): Promise<PlatformUserData> {
        const userCacheKey = this.cacheKey('user', user);
        const cached = await redis.fetch(userCacheKey);
        if (cached && await this.useCache()) {
            return JSON.parse(cached) as PlatformUserData;
        }
        const platformUser = await this.platformAdapter.login(user);
        await redis.store(userCacheKey, JSON.stringify(platformUser), 60 * 60 * 8); // Cache for 8 hours
        return platformUser;
    }

    private async resolveUserData(user: UserData): Promise<MttUserData | PlatformUserData> {
        if (this.gameAdapter.isMtt()) {
            const mttUserCacheKey = this.cacheKey('mtt-user', user);
            const cached = await redis.fetch(mttUserCacheKey);
            if (cached && await this.useCache()) {
                return JSON.parse(cached) as MttUserData;
            }
            const platformUser = await this.ensurePlatformUser(user);
            const mttUser = await this.platformAdapter.resolveMttUserData(platformUser);
            if (!mttUser?.mtt?.token) {
                throw new LoginError('No MTT token received');
            }
            await redis.store(mttUserCacheKey, JSON.stringify(mttUser), 60 * 60 * 24); // Cache for 24 hours
            return mttUser;
        }
        return this.ensurePlatformUser(user);
    }

    private async clearCache(user: UserData): Promise<void> {
        const userKey = this.cacheKey('user', user);
        const mttKey = this.cacheKey('mtt-user', user);
        await Promise.all([redis.remove(userKey), redis.remove(mttKey)]);
    }
}
