import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
    { languageOptions: { globals: globals.node } },
    pluginJs.configs.recommended,
    ...tseslint.configs.recommended,
    {
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'off',
        },
    },
    {
        files: ['./src/**/*.ts'],
        languageOptions: {
            parserOptions: {
                project: './tsconfig.json',
                tsconfigRootDir: import.meta.dirname,
            },
        },
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'off',
            '@typescript-eslint/await-thenable': 'warn',
            '@typescript-eslint/require-await': 'warn',
            '@typescript-eslint/no-floating-promises': 'warn',
            '@typescript-eslint/return-await': ['warn', 'never'],
        },
    },
    {
        files: ['./libs/pkw/**/*.ts'],
        ignores: [
            'libs/pkw/tests/**/*',
            'libs/pkw/src/proto/**/*',
            'libs/pkw/src/pkw_ts/tools/**/*',
        ],
        languageOptions: {
            parserOptions: {
                project: './libs/pkw/tsconfig.json',
                tsconfigRootDir: import.meta.dirname,
            },
        },
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'off',
            '@typescript-eslint/await-thenable': 'warn',
            '@typescript-eslint/require-await': 'warn',
            '@typescript-eslint/no-floating-promises': 'warn',
            '@typescript-eslint/return-await': ['warn', 'never'],
        },
    },
    {
        files: ['./libs/friends/**/*.ts'],
        languageOptions: {
            parserOptions: {
                project: './libs/friends/tsconfig.json',
                tsconfigRootDir: import.meta.dirname,
            },
        },
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'warn',
            '@typescript-eslint/await-thenable': 'warn',
            '@typescript-eslint/require-await': 'warn',
            '@typescript-eslint/no-floating-promises': 'warn',
            '@typescript-eslint/return-await': ['warn', 'never'],
        },
    },
    {
        ignores: [
            '**/dist/',
            'libs/pkw/tests/**/*',
            'libs/pkw/src/proto/**/*',
            'libs/pkw/src/pkw_ts/tools/**/*',
            'libs/mtt',
            'libs/wptgo',
            'src/local.ts',
        ],
    },
];
