import test from 'node:test';
import assert from 'node:assert/strict';

test('protos are not imported before pbVersion.version is set', async () => {
    // Import pbVersion directly
    const { default: pbVersion } = await import('../src/mtt/pb/version');
    assert.notEqual(pbVersion.version, undefined, 'pbVersion.version should start defined');

    pbVersion.version = undefined;

    // Import index.ts, but don’t trigger scan/play yet
    const { MttMain } = await import('../src/index');
    assert.equal(pbVersion.version, undefined, 'importing index should not preload protos');

    // Initialize
    MttMain.init(() => {}, async () => ({}), 'v0', {
        mttWorld: 'ws://test-world.com',
        mttGame: 'ws://test-game.com',
        mttApi: 'https://test-api.com',
    },);
    assert.equal(pbVersion.version, 'v0', 'pbVersion.version should be set in init()');
    // If mtt had touched protos too early, test would’ve failed already
});
