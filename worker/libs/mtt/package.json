{"name": "mtt", "version": "1.0.0", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "dependencies": {"axios": "^1.9.0", "bullmq": "^5.51.1", "https-proxy-agent": "^7.0.6", "protobufjs": "^6.11.4", "shared": "file:../shared", "websocket": "^1.0.35", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@types/node": "^22.15.3", "@types/websocket": "^1.0.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "scripts": {"build": "tsc && npm run cp_other_files", "cp_other_files": "cpx 'src/**/*.{js,d.ts,proto,json,csv}' dist", "test": "tsx --test"}, "author": "", "license": "ISC"}