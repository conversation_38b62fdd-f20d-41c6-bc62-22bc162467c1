export { DelayService, timeBucketLimit } from './delayService';
export { featureFlagValue } from './featureFlags';
export { fetchStrategy, weightedRandomSelection } from './strategy';
export { logging } from './logging';
export { JobContext } from './context';
export { GameState, CardString } from './pokerGameState';
export { getProxyAgent } from './proxy';
export * from './types';
export * from './constants';
export * from './evaluateHand';

export function randomStr(num: number, str?: string) {
    str = str || '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < num; i++) {
        result += str.charAt(Math.floor(Math.random() * str.length));
    }
    return result;
}

export function sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
