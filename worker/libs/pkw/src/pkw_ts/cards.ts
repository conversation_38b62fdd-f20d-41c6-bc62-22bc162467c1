import { CardItem } from './data/RoomData';
import { CardNum, CardSuit } from './tools/Enum';
import { CardString } from 'shared'


/**
 * @returns list of cards, or empty array if any card is hidden
 */
export const transformCardItemsToSymbol = (cards: CardItem[]): CardString[] | undefined => {
    // if there was a hidden card - we do not suggest the cards to be valid
    // therefore not showing anything
    if (cards.some((card) => (
        card.number === CardNum.CARD_HIDDEN ||
        card.suit === CardSuit.SUIT_HIDDEN
    ))) {
        return undefined;
    }
    return cards.map(cardToSymbol);
};


export const cardToSymbol = (card: CardItem): CardString => {
    const number = {
        [CardNum.CARD_2]: '2',
        [CardNum.CARD_3]: '3',
        [CardNum.CARD_4]: '4',
        [CardNum.CARD_5]: '5',
        [CardNum.CARD_6]: '6',
        [CardNum.CARD_7]: '7',
        [CardNum.CARD_8]: '8',
        [CardNum.CARD_9]: '9',
        [CardNum.CARD_10]: 'T',
        [CardNum.CARD_J]: 'J',
        [CardNum.CARD_Q]: 'Q',
        [CardNum.CARD_K]: 'K',
        [CardNum.CARD_A]: 'A',
        [CardNum.CARD_HIDDEN]: '?',
    }[card.number];

    const suit = {
        [CardSuit.CARD_DIAMOND]: 'd',
        [CardSuit.CARD_CLUB]: 'c',
        [CardSuit.CARD_HEART]: 'h',
        [CardSuit.CARD_SPADE]: 's',
        [CardSuit.SUIT_HIDDEN]: '?',
    }[card.suit];

    if (!suit || !number) {
        throw new Error(`Unknown card: ${JSON.stringify(card)}`);
    }
    return number + suit as CardString;
};
