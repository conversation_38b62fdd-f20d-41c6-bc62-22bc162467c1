import cv from './cv';
import { pb as world_pb } from '../proto/ws_protocol';
import { protocol as game_pb } from '../proto/gs_protocol';
import { ActionType, CreateGameMode } from './tools/Enum';
import {
    NoticeCommunityCards,
    NoticeGameBlind,
    NoticeGameElectDealer,
    NoticeGameHolecard,
    NoticeResetGame,
} from './data/RoomData';
import { cardToSymbol, transformCardItemsToSymbol } from './cards';
import pkwRoom from './pkwRoom';
import {
    DelayService,
    GameMode,
    GameType,
    JobDataHandler,
    logging,
    PlayerStats,
    SHARED_ACTION,
    UserStatus,
    GameState,
    sleep,
    StrategyResponseAction,
} from 'shared';
import { OpponentHand, ShowCardsFeature } from './features/showCards';
import { ExtraTimeFeature } from './features/extraTime';

const ProtobufActionToStrategyResponseAction = {
    [game_pb.ActionType.Enum_Action_Check]: StrategyResponseAction.CHECK,
    [game_pb.ActionType.Enum_Action_Fold]: StrategyResponseAction.FOLD,
    [game_pb.ActionType.Enum_Action_Call]: StrategyResponseAction.CALL,
    [game_pb.ActionType.Enum_Action_Bet]: StrategyResponseAction.BET,
    [game_pb.ActionType.Enum_Action_Raise]: StrategyResponseAction.RAISE,
    [game_pb.ActionType.Enum_Action_Allin]: StrategyResponseAction.ALLIN,
};

const ProtobufActionToSharedAction = {
    [ActionType.Enum_Action_Raise]: SHARED_ACTION.RAISE,
    [ActionType.Enum_Action_Allin]: SHARED_ACTION.ALL_IN,
    [ActionType.Enum_Action_Bet]: SHARED_ACTION.BET,
    [ActionType.Enum_Action_Check]: SHARED_ACTION.CHECK,
    [ActionType.Enum_Action_Call]: SHARED_ACTION.CALL,
    [ActionType.Enum_Action_Fold]: SHARED_ACTION.FOLD,
};

type ActionTimeType = {
    stopwatch?: number;
    immediate?: boolean;
};

const BOMP_MODE_CARDS_MAX_WAIT_MS = 2000;

let gameState: GameState = new GameState('', '', 0, 0);

export class pkwGame {
    private _selfUid: number = null;
    private _roomSt: world_pb.IClubGameSnapshotV3 = null;
    private _gameSt: game_pb.NoticeGameSnapshot = null;
    private _pActionTurn: game_pb.NoticePlayerActionTurn = null;
    private _selfInfo: game_pb.IPlayerInfo = null;

    private _greatestBet: number = 0; // the greatest bet amount in this round

    private _updateProgressCb: JobDataHandler = () => {};
    public _currentStack: number = 0; // TODO: duplicates `_selfInfo.stake`, check if it can be different
    private _previousGameStake: number = 0;
    private _lastWin: number = 0;
    private _lastKnownRake: number = 0;

    private delayService: DelayService;
    private _gameModeCode: GameMode = GameMode.NORMAL;
    private _gameTypeCode: GameType = GameType.NLHE;
    private _profileName?: string;
    private _rebuyDisabledByMaxRebuyCount: boolean = false;

    public updateInfo(roomSt?: world_pb.IClubGameSnapshotV3, gameSt?: game_pb.NoticeGameSnapshot) {
        this._selfUid = cv.dataHandler.getUserData().u32Uid;
        this._roomSt = roomSt;
        this._gameSt = gameSt;
        this._gameTypeCode =
            this._roomSt.game_mode === CreateGameMode.CreateGame_Mode_Short
                ? GameType.SHORTDECK
                : GameType.NLHE;
        logging.info(`[state] updateInfo:`, {
            ante: this._roomSt.ante,
            big_blind: this._roomSt.big_blind,
            gameModeCode: this._gameModeCode,
            gameTypeCode: this._gameTypeCode,
        });
        this.resetGameState();
        logging.setUserId(this._selfUid);
        logging.setRoomId(this._gameSt.roomid);
    }

    resetGameState() {
        const gameuuid = this._roomSt.room_id + ':' + this._selfUid + ':' + this._roomSt.start_time;
        gameState = new GameState(
            gameuuid,
            this._gameSt.roomid.toString(),
            this._gameTypeCode === GameType.SHORTDECK ? this._roomSt.ante : this._roomSt.big_blind,
            this._roomSt.ante,
            this._gameModeCode,
            this._gameTypeCode,
        );
    }

    public init() {
        this._regMsg();
    }

    public getSelfUid() {
        return this._selfUid;
    }

    public updateStatus(status: UserStatus) {
        this._updateProgressCb({ stats: this.getPlayerStats(), status: status });
    }

    public setUpdateProgressCb(cb: JobDataHandler) {
        this._updateProgressCb = cb;
    }

    public setGameModeCode(gameModeCode: GameMode) {
        this._gameModeCode = gameModeCode;
    }

    public setDelayService(delayService: DelayService) {
        this.delayService = delayService;
    }

    public setProfileName(profileName?: string) {
        this._profileName = profileName;
    }

    private _regMsg() {
        cv.MessageCenter.register('on_game_holecard_noti', this.OnGameHoleCardNoti.bind(this));
        cv.MessageCenter.register('on_game_action_turn_noti', this.OnActionTurn.bind(this));
        cv.MessageCenter.register('on_game_action_noti', this.OnPlayerAction.bind(this));
        cv.MessageCenter.register('on_game_elect_dealer_noti', this.OnGameElectDealer.bind(this));
        cv.MessageCenter.register('on_game_communitycard_noti', this.OnCommunityCard.bind(this));
        cv.MessageCenter.register('on_game_blind_noti', this.OnGameBlindNoti.bind(this));
        cv.MessageCenter.register('on_resetgame_noti', this.OnResetGameNoti.bind(this));
        cv.MessageCenter.register('on_game_anti_noti', this.OnAnteNoti.bind(this));
        cv.MessageCenter.register('on_game_settlement_noti', this.onGameSettlementNotice.bind(this));
        cv.MessageCenter.register('on_room_situation', this.onRoomSituation.bind(this));
        cv.MessageCenter.register('on_game_showdown_noti', this.onGameShowdownNoti.bind(this));
    }

    private async onGameShowdownNoti(msg: game_pb.NoticeGameShowDown) {
        try {
            logging.info('[PKW_GAME] onGameShowdownNoti', { payload: msg });
            const hero = gameState.getPlayerById(this._selfUid.toString());
            if (!hero) {
                logging.info(
                    '[PKW_GAME] onGameShowdownNoti - hero not found; probably not participating in the game',
                );
                return;
            }
            const heroHasFolded = gameState.playerHasFolded(hero);
            if (!heroHasFolded) {
                return;
            }

            const communityCards = gameState.getCommunityCards();
            const gamePlayer = gameState.getPlayerById(this._selfUid.toString());
            const holeCards = gameState.getPlayerCards(gamePlayer);

            const opponentHands: OpponentHand[] = msg.shows
                .filter((show) => show.playerid !== this._selfUid)
                .map((show) => ({ cardSymbols: show.cards.map(cardToSymbol) }));

            const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
            if (result.length) {
                logging.info('[SHOW_CARDS_FEATURE] - show card indexes result: ', result);
                if (result.length === 1) {
                    cv.gameNet.RequestShowCard(this._gameSt.roomid, result[0], true);
                } else {
                    cv.gameNet.RequestShowCard(this._gameSt.roomid, result, true);
                }
            }
        } catch (error) {
            logging.error('[PKW_GAME] error occured in onGameShowdownNoti', error);
        }
    }

    private OnGameHoleCardNoti(msg: NoticeGameHolecard) {
        logging.withTag('PKW_GAME').info('[InGame] OnGameHoleCardNoti', { payload: msg });
        if (this._selfUid == null) {
            logging.warn('[InGame] OnGameHoleCardNoti - selfUid is null');
            return;
        }

        // There could be a case where the card in OnGameHoleCardNoti are hidden (number: 256, suit: 256)
        const holeCards = transformCardItemsToSymbol(msg.holdcards);
        if (!holeCards) {
            logging.warn('[InGame] OnGameHoleCardNoti  - holecards are hidden... skipping', {});
            return;
        }

        try {
            const gamePlayer = gameState.getPlayerById(this._selfUid.toString());
            gameState.setPlayerCards(gamePlayer, holeCards);
        } catch (error) {
            logging.error('[InGame] OnGameHoleCardNoti - Error setting hole cards', error);
        }
    }

    public updateCurrentStack(_newStack: number, source?: string) {
        // Additional check to ensure the new stack is a number and not a protobuf long integer
        const newStack = Number(_newStack);
        logging.info(`Current stack updated: ${this._currentStack} -> ${newStack}`, { source });
        this._currentStack = newStack;
        logging.setStack(newStack);
    }

    private onRoomSituation(situation: game_pb.NoticeRoomSituation) {
        logging.withTag('SDK').info('[PKW_GAME] onRoomSituation', situation);

        const player = situation.buyin_player_list.find((p) => p.playerid == this._selfUid);
        if (!player) {
            return;
        }

        pkwRoom.setTotalBuyIn(Number(player.total_buyin));
        pkwRoom.setHandsPlayed(Number(player.HandCount));

        const newStack = Number(player.total_buyin) + Number(player.curr_record);
        this.updateCurrentStack(newStack);
        this.checkIfActualBuyInIsLowerThanRequestedBuyIn();
        this.checkIfRebuyNeeded();
        // Withdraw is feature that allows user to withdraw chips during the game if the stake is too high
        this.checkIfWithdrawNeeded();
    }

    private onGameSettlementNotice(msg: game_pb.NoticeGameSettlement) {
        // write down current stake on the end of the game
        this._lastWin = msg.winners.find((w) => w.playerid == this._selfUid)?.amount || 0;
        this._previousGameStake = (this._selfInfo?.stake ?? 0) + this._lastWin;
        logging.info(
            `[InGame] onGameSettlementNotice: Stake - ${this._previousGameStake}, Win - ${this._lastWin}`,
        );
    }

    private OnAnteNoti(msg: any) {
        logging.info('[InGame] OnAnteNoti', msg);
        if (msg && msg.amount_list) {
            const ante = Math.max(...msg.amount_list);
            if (ante > this._roomSt?.ante) {
                logging.info(`[state] Changed game mode: ${gameState.game_mode_code} --> ${GameMode.BOMB};`, {
                    ante,
                    currentAnte: this._roomSt?.ante,
                    currentBigBlind: this._roomSt.big_blind,
                });
                gameState.setGameParams({
                    ante,
                    game_mode_code: GameMode.BOMB,
                });
            }
        }
    }

    private OnGameElectDealer(msg: NoticeGameElectDealer) {
        logging.info('[InGame] OnGameElectDealer', msg);

        gameState.setGameParams({
            sb_seat: this._gameTypeCode !== GameType.SHORTDECK ? msg.sb_seateid : undefined,
            bb_seat: this._gameTypeCode !== GameType.SHORTDECK ? msg.bb_seatid : undefined,
            dealer_seat: msg.dealer_seatid,
        });
    }

    public OnResetGameNoti(msg: NoticeResetGame) {
        logging.resetRoundValues();

        // Zoom: the room can be changed with each new hand, need to update the room id
        if (this._gameSt && msg.roomid && this._gameSt.roomid != msg.roomid) {
            logging.info('OnResetGameNoti: roomId changed', {
                prevRoomId: this._gameSt.roomid,
                newRoomId: msg.roomid,
                payload: msg,
            });
            this._gameSt.roomid = msg.roomid;
            logging.setRoomId(this._gameSt.roomid);
        }

        if (this._roomSt) {
            logging.info('OnResetGameNoti: Resetting game state', {
                ante: this._roomSt.ante,
                bigBlind: this._roomSt.big_blind,
            });
            this.resetGameState();
        } else {
            logging.warn('OnResetGameNoti: Room state is null, cannot reset game state');
        }
        msg.players.forEach((player) => {
            if (!player.in_game || player.stake <= 0) {
                return;
            }
            gameState.addPlayer(player.playerid.toString(), player.seatid, player.stake);
            if (player.playerid == this._selfUid) {
                this.updateCurrentStack(player.stake, 'OnResetGameNoti');
            }
        });
        logging.info('OnResetGameNoti: Players after reset', gameState.players);
    }

    private checkIfActualBuyInIsLowerThanRequestedBuyIn() {
        // in some case the real buyIn can be lower than we requested
        logging
            .withTag('BUYIN')
            .info(
                `[InGame] checkIfActualBuyInIsLowerThanRequestedBuyIn - beginGameStake: ${this._currentStack}, previousGameStake: ${this._previousGameStake}`,
            );
        const actualBuyIn = this._currentStack - this._previousGameStake;
        if (this._lastWin > 0 && !pkwRoom.getRebuyWasMade()) {
            // If players wins, the rake will be taken from him
            // We can understand it by compairing the previous game final stake and the stake at the beginning of the game
            // If player won, and requested rebuy in the last game, he will het + rebuy - rake, that's why we won't show rake here
            // The formula for rake calculation is currently unknown
            this._lastKnownRake = this._previousGameStake - this._currentStack;
            logging
                .withTag('BUYIN')
                .info(
                    `[InGame] checkIfActualBuyInIsLowerThanRequestedBuyIn - RAKE: ${this._lastKnownRake}, Last Win: ${this._lastWin}`,
                );
        }

        if (pkwRoom.getRebuyWasMade()) {
            if (actualBuyIn + this._lastKnownRake < pkwRoom.getBuyInAmount()) {
                logging
                    .withTag('BUYIN')
                    .info(
                        `[InGame] checkIfActualBuyInIsLowerThanRequestedBuyIn - rebuy (${actualBuyIn}) was made ` +
                            `and it is lower than requested buyIn (${pkwRoom.getBuyInAmount()})`,
                    );
            }

            pkwRoom.setRebuyWasMade(false);
        }
    }

    // private checkAutoBuyIn() {
    //     // We need this if user returns to the room and does not need to buy in or the buy was made automatically
    //     if (pkwRoom.getBuyInAmount() == 0) {
    //         pkwRoom.setBuyInAmount(this._currentStack);
    //     }
    // }

    private getPlayerStats(): PlayerStats {
        return {
            handsPlayed: pkwRoom.getHandsPlayed(),
            totalBuyIn: pkwRoom.getTotalBuyIn(),
            lastBuyIn: pkwRoom.getBuyInAmount(),
            rebuyCount: pkwRoom.getRebuyCount(),
            stack: this._currentStack,
        };
    }

    private OnGameBlindNoti(msg: NoticeGameBlind) {
        logging.info('[InGame] OnGameBlindNoti', { payload: msg });
        if (msg.straddle_seat_list.length > 0) {
            gameState.setGameParams({ straddle_seat: msg.straddle_seat_list[0] });
        }
        if (msg.post_seat_list.length > 0) {
            gameState.post_seats = msg.post_seat_list;
        }
    }

    private updateGreatestBet() {
        this._greatestBet = 0;
        for (const player of this._pActionTurn.players) {
            if (player.playerid == this._selfUid) {
                this._selfInfo = player;
                logging.setPlayerName(player.name);
            }
            this._greatestBet = Math.max(this._greatestBet, player.round_bet);
        }
    }

    private getCurrentPot(): number {
        let pot: number = 0;
        this._pActionTurn.pots.forEach((p) => {
            pot += p.amount;
        });
        return pot;
    }

    //your move now, send your action
    private async OnActionTurn(pkActionTurn: game_pb.NoticePlayerActionTurn): Promise<void> {
        // if it's not our turn, we don't need to act
        this._pActionTurn = pkActionTurn;
        this.updateGreatestBet();
        if (pkActionTurn.curr_action_uid !== this._selfUid) {
            return;
        }
        logging.info('[InGame] OnActionTurn');
        const stopwatch = Date.now();

        let currentPlayer = gameState.getPlayerById(this._selfUid.toString());
        if (!currentPlayer) {
            const actionTurnPlayer = pkActionTurn.players.find((p) => p.playerid === this._selfUid);
            if (actionTurnPlayer) {
                logging.warn(
                    '[InGame] OnActionTurn - self player not found, adding player from pkActionTurn',
                    { actionTurnPlayer },
                );
                gameState.addPlayer(
                    actionTurnPlayer.playerid.toString(),
                    actionTurnPlayer.seatid,
                    actionTurnPlayer.stake,
                );
                currentPlayer = gameState.getPlayerById(this._selfUid.toString());
            } else {
                logging.error(
                    '[InGame] OnActionTurn - self player not found, calling fallback action...',
                    'Self player not found',
                    { selfId: this._selfUid, state: gameState, pkActionTurn },
                );
                return this.callFallbackAction(stopwatch);
            }
        }

        if (pkActionTurn.holdcards) {
            const gamePlayer = gameState.getPlayerById(this._selfUid.toString());
            const holeCards = transformCardItemsToSymbol(pkActionTurn.holdcards);
            gameState.setPlayerCards(gamePlayer, holeCards);
        } else {
            return this.callFallbackAction(stopwatch);
        }

        pkwRoom.setUserStatus(UserStatus.inGamePlay);

        // in bomb pot mode, wait for community cards event if it comes after the action turn event
        while (
            gameState.game_mode_code === GameMode.BOMB &&
            gameState.actions.entries.length === 0 &&
            Date.now() - stopwatch < BOMP_MODE_CARDS_MAX_WAIT_MS
        ) {
            await sleep(200);
        }

        // We do not send userId here because we don't want to set betProfile
        // We send profileName to set strategyProfile
        try {
            // const fetchResult = await gameState.fetchStrategy({
            //     profileName: this._profileName,
            //     betProfileUserId: undefined,
            // });
            const action = StrategyResponseAction.ALLIN;

            let actionTime: ActionTimeType = { stopwatch };

            const blindValue =
                this._gameTypeCode === GameType.SHORTDECK && this._roomSt.big_blind === 0
                    ? this._roomSt.ante
                    : this._roomSt.big_blind;

            // const extraTimeParameters = await ExtraTimeFeature.getFeatureParameters({
            //     gamePhase: gameState.getGamePhase(),
            //     strategyActions: fetchResult.strategyActions,
            //     greatestBet: this._greatestBet,
            //     selfInfoRoundBet: this._selfInfo.round_bet,
            //     blindValue,
            //     currentPot: this.getCurrentPot(),
            // });

            // if (extraTimeParameters.isApplicable) {
            //     const hasPassedMs = Date.now() - stopwatch;
            //     const waitBeforeRequestMs = extraTimeParameters.waitBeforeRequestMs - hasPassedMs;
            //     if (waitBeforeRequestMs > 0) {
            //         logging.info(
            //             `[OnActionTurn][ExtraTimeFeature] waiting ${waitBeforeRequestMs} ms before requesting extra time`,
            //             {
            //                 extraTimeParameters,
            //             },
            //         );
            //         await sleep(waitBeforeRequestMs);
            //     } else {
            //         logging.info(`[OnActionTurn][ExtraTimeFeature] requesting extra time`, {
            //             extraTimeParameters,
            //         });
            //     }

            //     this._requestAddingExtraTime();

            //     await sleep(extraTimeParameters.waitAfterRequestMs);
            //     actionTime = { immediate: true };
            // }
            logging.info(`[ACTION] decision to make action: ${action}`, { actionTime });

            // const requestedAction =
            //     action === StrategyResponseAction.BET ? StrategyResponseAction.RAISE : action;
            return this._reqAction(
                StrategyResponseAction.ALLIN,
                null,
                1,
                actionTime,
            );
        } catch (err) {
            logging.error(`[ACTION] fetchStrategy error`, err?.message || err, { gameState });

            return this.callFallbackAction(stopwatch);
        }
    }

    private _requestAddingExtraTime() {
        void this.cvGameNetRequestAction(ActionType.Enum_Action_AddActionTime, 0, null, {
            immediate: true,
        });
    }

    private callFallbackAction(stopwatch: number) {
        const fallbackAction =
            this._greatestBet === this._selfInfo.round_bet
                ? StrategyResponseAction.CHECK
                : StrategyResponseAction.FOLD;
        logging.info(`PkwGame callFallbackAction: ${fallbackAction}`);
        return this._reqAction(fallbackAction, null, 0, { stopwatch });
    }

    private checkIfWithdrawNeeded() {
        if (pkwRoom.getWithdrawAmount() <= 0) {
            // withdraw is disabled
            return;
        }
        // We use ante for calculating the withdraw amount if the game is shortdeck
        const blindValue =
            this._gameTypeCode === GameType.SHORTDECK ? this._roomSt.ante : this._roomSt.big_blind;
        const shouldWithdrawAt = blindValue * pkwRoom.getWithdrawThreshold();
        const withdrawNeeded = this._currentStack > shouldWithdrawAt;
        logging
            .withTag('SDK')
            .info(
                `[PKW_GAME] checkIfWithdrawNeeded; Current stake: ${this._currentStack}, Blind value: ${blindValue}, Should withdraw at: ${shouldWithdrawAt}, Withdraw needed: ${withdrawNeeded}`,
            );
        if (withdrawNeeded) {
            pkwRoom.requestBuyOut();
        }
    }

    private checkIfRebuyNeeded() {
        if (!pkwRoom.getRebuyEnabled()) {
            logging.info('[InGame] Rebuy is disabled');
            return;
        }
        if (this._selfInfo == null) {
            return;
        }

        const baseValue =
            this._gameTypeCode === GameType.SHORTDECK ? this._roomSt.ante : this._roomSt.big_blind;

        const shouldRebuyAt = baseValue * pkwRoom.getRebuyThreshold();
        const rebuyNeeded = this._currentStack < shouldRebuyAt;
        logging.info('[PKW_GAME] checkIfRebuyNeeded', {
            stack: this._currentStack,
            minBuyIn: this._roomSt.buyin_min,
            baseValue,
            shouldRebuyAt,
            rebuyNeeded,
        });
        if (rebuyNeeded && !this.leaveRoomIfRebuyLimitReached()) {
            pkwRoom.rebuy();
        }
    }

    public leaveRoomIfRebuyLimitReached(leaveImmediately: boolean = false): boolean {
        logging.info(
            `[PKW_GAME] checking max rebuy count ${pkwRoom.maxRebuyCount}, current rebuy count ${this.getPlayerStats().rebuyCount}`,
        );
        const maxRebuyCountReached = this.getPlayerStats().rebuyCount >= pkwRoom.maxRebuyCount;
        if (maxRebuyCountReached) {
            if (this._rebuyDisabledByMaxRebuyCount) {
                return true;
            }
            this._rebuyDisabledByMaxRebuyCount = true;
            const leaveRoomTimeout = leaveImmediately ? 0 : 60 + Math.random() * 120; // 1 to 3 minutes, or leave immediately
            logging.info(
                `[PKW_GAME] Max rebuy count reached, will leave room in ${leaveRoomTimeout} seconds`,
            );

            setTimeout(async () => {
                await pkwRoom.leaveRoom();
                logging.info(
                    `[PKW_GAME] Left room ${leaveRoomTimeout} seconds after max rebuy count reached`,
                );
                pkwRoom.stopGame();
            }, leaveRoomTimeout * 1000);
        }
        return maxRebuyCountReached;
    }

    // private printCards(cards: CardItem[], banner: string) {
    //     console.log(banner);
    //     cards.forEach((card) => {
    //         console.log(
    //             '[ ' +
    //                 this._getEnumName(CardNum, card.number) +
    //                 ' : ' +
    //                 this._getEnumName(CardSuit, card.suit) +
    //                 ' ]',
    //         );
    //     });
    //     console.log('-----------END Print Cards------------');
    // }

    private _reqAction(
        roundAction: StrategyResponseAction,
        raiseAmount: number | null,
        actionProbability: number | null,
        actionTime: ActionTimeType,
    ): Promise<void> {
        switch (roundAction) {
            case StrategyResponseAction.FOLD:
                return this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Fold,
                    0,
                    actionProbability,
                    actionTime,
                );
            case StrategyResponseAction.CHECK:
                if (this._greatestBet - this._selfInfo.round_bet > 0) {
                    logging.warn('[ACTION] illegal move: CHECK, change to call action');
                    return this._reqAction(StrategyResponseAction.CALL, null, actionProbability, actionTime);
                }
                return this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Check,
                    0,
                    actionProbability,
                    actionTime,
                );
            case StrategyResponseAction.CALL: {
                const callAmount = this._greatestBet - this._selfInfo.round_bet;
                if (this._selfInfo.stake < callAmount) {
                    logging.warn('[ACTION] insufficient stake to call, change to allin action', {
                        amount: (callAmount / 100).toFixed(2),
                    });
                    return this._reqAction(StrategyResponseAction.ALLIN, null, actionProbability, actionTime);
                }
                return this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Call,
                    callAmount,
                    actionProbability,
                    actionTime,
                );
            }
            case StrategyResponseAction.ALLIN: {
                const allInAmount = this._selfInfo.stake + this._selfInfo.round_bet;
                return this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Allin,
                    allInAmount,
                    actionProbability,
                    actionTime,
                );
            }
            case StrategyResponseAction.RAISE: {
                const minToCall = this._greatestBet - this._selfInfo.round_bet;

                raiseAmount = raiseAmount || this._pActionTurn.minimum_bet_i64; //- (minToCall + this._selfInfo.round_bet);
                if (this._selfInfo.stake < raiseAmount || this._selfInfo.stake < minToCall) {
                    logging.warn('[ACTION] insufficent stake to raise, change to allin action', {
                        raiseAmount: (raiseAmount / 100).toFixed(2),
                        minToCall: (minToCall / 100).toFixed(2),
                    });
                    return this._reqAction(StrategyResponseAction.ALLIN, null, actionProbability, actionTime);
                }
                return this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Raise,
                    raiseAmount,
                    actionProbability,
                    actionTime,
                );
            }
            default:
                logging.error(`[ACTION] illegal action, change to fold action`, null, { roundAction });
                return this._reqAction(StrategyResponseAction.FOLD, null, actionProbability, actionTime);
        }
    }

    private async cvGameNetRequestAction(
        actionType: ActionType,
        amount: number,
        actionProbability: number | null,
        actionTime: ActionTimeType,
    ): Promise<void> {
        const sharedAction = ProtobufActionToSharedAction[actionType] || SHARED_ACTION.FOLD;
        const roomId = this._gameSt.roomid;
        const actionSeq = this._pActionTurn.ActionSeq;

        const gamePhase = gameState.getGamePhase();

        const isCheckBet = actionType === ActionType.Enum_Action_Fold ? true : undefined;
        const keepEnd = actionType === ActionType.Enum_Action_Fold ? 0 : undefined;

        // Action time management
        if (!actionTime.immediate) {
            const calculatedDelayMs = this.delayService.calculateDelayMs(
                gamePhase,
                sharedAction,
                actionProbability,
            );

            const { stopwatch = Date.now() } = actionTime;
            const timePassed = new Date().getTime() - stopwatch;

            const delayLeftMs = calculatedDelayMs - timePassed;
            logging
                .withTag('DELAY')
                .info(
                    `Calculated delay: ${calculatedDelayMs} (ms) for ${SHARED_ACTION[sharedAction]} on stage ${gamePhase.toString()}, delay left: ${delayLeftMs} ms`,
                );

            if (delayLeftMs > 0) {
                await sleep(delayLeftMs);
            }
        }

        logging.info(
            `[ACTION] cvGameNetRequestAction - request action ${ActionType[actionType]}, amount: ${amount}`,
        );
        cv.gameNet.RequestAction(roomId, actionType, amount, isCheckBet, keepEnd, actionSeq);
    }

    private OnPlayerAction(pkPlayerAction: game_pb.NoticePlayerAction) {
        const actionPlayer = this._pActionTurn?.players?.find(
            (p) => p.seatid === pkPlayerAction.last_action_seat_id,
        );
        const isHero = actionPlayer?.playerid === this._selfUid;
        logging.info(
            `[OnPlayerAction] player: ${actionPlayer?.name} ${isHero ? '(hero)' : ''} request action: ${ActionType[pkPlayerAction.action_type]}, amount: ${pkPlayerAction.amount}`,
        );

        const gameAction = ProtobufActionToStrategyResponseAction[pkPlayerAction.action_type];
        gameState.addAction(gameAction, pkPlayerAction.amount, pkPlayerAction.last_action_seat_id);
        this.updateStatus(pkwRoom._pkwUser.status);
    }

    private OnCommunityCard(pkCommunityCards: NoticeCommunityCards) {
        const comCards = transformCardItemsToSymbol(pkCommunityCards.cards);
        logging.info(`[InGame] OnCommunityCard: ${comCards}`, {
            payload: pkCommunityCards,
        });
        gameState.addCardsAction(comCards);
    }

    // private _getEnumName(enumType: any, enumValue: number): string {
    //     return enumType[enumValue];
    // }

    private static g_instance: pkwGame;

    public static getInstance(): pkwGame {
        if (!pkwGame.g_instance) {
            pkwGame.g_instance = new pkwGame();
        }
        return pkwGame.g_instance;
    }
}

export default pkwGame.getInstance();
