import { dataHandler } from './data/DataHandler';
import { RoomManager } from './data/RoomManager';
import { DataNetWork } from './network/DataNetWork';
import { GameNetWork } from './network/GameNetWork';
import { HttpHandler } from './network/HttpHandler';

import { NetWorkManager } from './network/NetWorkManager';
import { NetWork } from './network/NetWork';
import { WorldNetWork } from './network/WorldNetWork';

import { Config } from './tools/Config';

import * as Enums from './tools/Enum';

import { MessageCenter } from './tools/MessageCenter';
import { Tools } from './tools/Tools';
import { logging } from 'shared';

import { RoomData } from './data/RoomData';
import { ServerErrorCodes } from '../serverErrorCodes';

class cv {
    public gamePB: any;
    public worldPB: any;
    public dataPB: any;
    public gatePB: any;

    public tools: Tools;
    public httpHandler: HttpHandler;

    public Enum = Enums;
    public dataHandler: typeof dataHandler = dataHandler;
    public MessageCenter = MessageCenter;
    public config: Config;

    //net works
    public netWork: NetWork;
    public worldNet: WorldNetWork;
    public gameNet: GameNetWork;
    public dataNet: DataNetWork;
    public roomManager: RoomManager;
    public netWorkManager: NetWorkManager;
    public GameDataManager = {
        tRoomData: new RoomData(),
    };

    public initCV() {

        this.httpHandler = HttpHandler.getInstance();

        this.tools = Tools.getInstance();
        this.netWork = NetWork.getInstance();
        this.config = Config.getInstance();
        this.netWorkManager = NetWorkManager.getInstance();
        this.roomManager = RoomManager.getInstance();
        this.worldNet = WorldNetWork.getInstance();
        this.gameNet = GameNetWork.getInstance();
        this.dataNet = DataNetWork.getInstance();
    }

    public ToastError(i32Error: number) {
        logging.warn(`[SDK] ToastError ${i32Error}: ${ServerErrorCodes[i32Error]}`);
    }

    private static g_instance: cv;
    public static getInstance(): cv {
        if (!cv.g_instance) {
            cv.g_instance = new cv();
        }
        return cv.g_instance;
    }

    getLocation() {
        return {
            latitude: 10,
            longitude: 10,
        };
    }
}

export default cv.getInstance();
