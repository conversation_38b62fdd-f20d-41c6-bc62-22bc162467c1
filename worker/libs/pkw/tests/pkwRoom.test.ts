import { test, mock } from 'node:test';
import assert from 'node:assert/strict';

import pkwRoom from '../src/pkw_ts/pkwRoom';
import { protocol as game_pb } from '../src/proto/gs_protocol'
import { User } from '../src/pkw_ts/pkwUser';
import cv from '../src/pkw_ts/cv';
import { RoomManager } from '../src/pkw_ts/data/RoomManager';
import { UnrecoverableError } from 'bullmq';
import { sleep } from 'shared';

test('pkwRoom _getAvailableSeatId', () => {

	pkwRoom._pkwUser = {
		userId: 42,
	} as User;
	pkwRoom['_gameSt'] = {
		tstate: {
			players: [
				{ playerid: 1, seatid: 0 },
				{ playerid: 2, seatid: 1 },
				{ playerid: 3, seatid: 2 },
				{ playerid: 4, seatid: 6 },
			]
		}
	} as game_pb.NoticeGameSnapshot;
	pkwRoom['_roomSt'] = {
		player_count_max: 8,
	};

	assert.equal(pkwRoom['_getAvailableSeatId'](), 3);

	pkwRoom['_roomSt'] = {
		player_count_max: 3,
	};
	assert.equal(pkwRoom['_getAvailableSeatId'](), -1);

	pkwRoom['_gameSt'].tstate!.players![2].playerid = 42;
	assert.equal(pkwRoom['_getAvailableSeatId'](), 2);
});

test('pkwRoom _onSeatOccupied', () => {
	cv.roomManager = RoomManager.getInstance();
	const requestSitdownMock = mock.fn();
	cv.gameNet = {
		RequestSitdown: requestSitdownMock,
	} as any;
	pkwRoom.init();

	cv.roomManager.setCurrentRoomID(42);

	pkwRoom['_getAvailableSeatId'] = () => 69;

	// if bought in and sat down, ignore seatOccupied
	pkwRoom['_isSatDown'] = true;
	pkwRoom['_isBoughtIn'] = true;
	cv.MessageCenter.send('seatOccupied', null);
	assert.equal(requestSitdownMock.mock.callCount(), 0);

	// else try to sit down to found sopt
	pkwRoom['_isSatDown'] = false;
	cv.MessageCenter.send('seatOccupied', null);
	assert.equal(requestSitdownMock.mock.callCount(), 1);
	assert.deepEqual(requestSitdownMock.mock.calls[0].arguments, [42, 69, false]);

	// if no spot found and not in room - do nothing
	pkwRoom['_getAvailableSeatId'] = () => -1;
	pkwRoom['_onUserActionDone'] = () => {};
	pkwRoom['_doNextUserAction'] = () => {};
	const requestLeaveRoomMock = mock.fn();
	(cv.roomManager as any).RequestLeaveRoom = requestLeaveRoomMock;
	cv.roomManager.setCurrentRoomID(0);

	cv.MessageCenter.send('seatOccupied', null);
	assert.equal(requestLeaveRoomMock.mock.callCount(), 0);

	// if no spot found and in room - leave room
	cv.roomManager.setCurrentRoomID(42);
	cv.MessageCenter.send('seatOccupied', null);
	assert.equal(requestLeaveRoomMock.mock.callCount(), 1);

});

test('pkwRoom _onBuyInFailed raises UnrecoverableError', () => {
	pkwRoom.init();
	cv.roomManager = RoomManager.getInstance();
	cv.roomManager.setCurrentRoomID(42);
	const requestLeaveRoomMock = mock.fn();
	(cv.roomManager as any).RequestLeaveRoom = requestLeaveRoomMock;

	const recoverableErrors = [1,2,3,100,200,300,1000,2000,3000]; // random numbers
	for (const code of recoverableErrors) {
		cv.MessageCenter.send('onBuyInFailed', code);
		assert(Number.isInteger(pkwRoom['_currentError']));
		assert(requestLeaveRoomMock.mock.callCount() > 0);
		requestLeaveRoomMock.mock.resetCalls();
	}

	const unrecoverableErrors = [39, 114, 515, 1301, 1006];
	for (const code of unrecoverableErrors) {
		cv.MessageCenter.send('onBuyInFailed', code);
		assert(pkwRoom['_currentError'] instanceof UnrecoverableError);
		assert(requestLeaveRoomMock.mock.callCount() > 0);
		requestLeaveRoomMock.mock.resetCalls();
	}
});

test('pkwRoom _onGetTables throws UnrecoverableError when room not found', async () => {
    const sharedModule = await import('shared');
    mock.method(sharedModule, 'sleep', () => Promise.resolve());

    pkwRoom.init();
    cv.roomManager = RoomManager.getInstance();
    cv.roomManager.setCurrentRoomID(42);
    pkwRoom['_joinRoomId'] = 100;
    pkwRoom['_getTablesAttempts'] = 0;
    pkwRoom['_doNextUserAction'] = () => { cv.MessageCenter.send('onGetTables', []) };

    const requestLeaveRoomMock = mock.fn();
    (cv.roomManager as any).RequestLeaveRoom = requestLeaveRoomMock;

    await pkwRoom['_onGetTables']([]);
	await sleep(1); // wait for async code

    assert(pkwRoom['_currentError'] instanceof UnrecoverableError);
    assert(requestLeaveRoomMock.mock.callCount() > 0);
    requestLeaveRoomMock.mock.resetCalls();
});
