{"devDependencies": {"@types/node": "^22.15.3", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "bullmq": "^5.51.1", "long": "^5.3.2", "pako": "^2.1.0", "protobufjs": "^7.5.2", "shared": "file:../shared", "tsconfig-paths": "^4.2.0", "ws": "^8.18.1"}, "name": "pkw", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && npm run cp_other_files", "cp_other_files": "cpx 'src/**/*.{js,d.ts,proto,json,csv}' dist", "test": "tsx --test"}}