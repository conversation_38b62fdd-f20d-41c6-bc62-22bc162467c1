import * as HttpApi from './http_api';
import { CreateRoomParams } from './http_api';
import {
    connectToGameWebsocket,
    exitRoom,
    onExitedRoomPromise,
    sendJoinRoom,
    sendRequestSeat,
} from './custom_websocket';
import { GameError, PlayType } from './types';
import {
    AppType,
    CurrencyType,
    JobDataHandler,
    PlatformUserData,
    PlayerStatus,
    sleep,
    TableData,
    UserStatus,
} from 'shared';
import { createLogger, setLoggingContext } from './logging';
import { BattleRoomMsg } from 'protobuf/MsgDeliverRespProto';
import * as GameLogic from './game_logic';
import { RoomMode } from 'pkw';

const console = createLogger('FriendsMain');

type FriendsUrlConfig = {
    wpkHttpURL: string;
    wpkGameWsURL: string;
};

export type CreateFriendsRoomParams = CreateRoomParams;

export const FriendsMain = {
    urlConfig: undefined as FriendsUrlConfig,

    init(urlConfig: FriendsUrlConfig) {
        this.urlConfig = urlConfig;
    },

    async play(
        user: PlatformUserData,
        roomId: number,
        handler: JobDataHandler,
        createRoomParams?: CreateRoomParams,
    ): Promise<void> {
        setLoggingContext({ userId: user.userId, roomId });
        GameLogic.setJobDataHandler(handler);
        HttpApi.setCurrentUser(user);

        if (!roomId) {
            if (createRoomParams) {
                console.info(`Creating room for user ${user.userId}`, {
                    userId: user.userId,
                    createRoomParams,
                });
                const response = await HttpApi.createRoom(createRoomParams);
                if (response.errorCode) {
                    throw new GameError('Cannot create room: ' + response.errMsg, response.errorCode);
                }

                const roomsRecord = await HttpApi.getRoomsCreatedByUser();
                roomId = roomsRecord.roomList[0].roomId as number;
                console.info(`Created room ${roomId}`);
            } else {
                throw new GameError('Room ID or new room creation params required for play jobType');
            }
        }
        handler({ status: UserStatus.loggedOn });
        await joinRoomAndPlay(user, roomId, this.urlConfig.wpkGameWsURL, undefined);
        console.info(`Bot finished for user ${user.userId}`, { userId: user.userId, roomId });
    },

    scan(user: PlatformUserData, clubId: number | undefined, handler: JobDataHandler): Promise<void> {
        setLoggingContext({ userId: user.userId, clubId });
        HttpApi.setCurrentUser(user);
        return scanPlayerTables(clubId, handler);
    },

    stop(): Promise<void> {
        return exitRoom();
    },
};

/*
 see CreateRoomParams
 room.playType 0=normal, 1=shortdeck, 2=omaha, 3=sng
*/
function toTableData(room: BattleRoomMsg): TableData {
    const blinds = [+room.gradeCfg.smallBlind, +room.gradeCfg.bigBlind];
    if (room.isThirdBlind) {
        const straddleMultiplier = room.gradeCfg['straddleFeeMul']; // not in protobuf
        blinds.push(+room.gradeCfg.smallBlind * straddleMultiplier);
    }
    const playersCount = room['sitPersonNum'] as number; // not in protobuf

    return {
        tableId: room.roomId as number,
        gameType: 'NLHE',
        gameId: -1,
        gameMode: 0, // 2 for multiflight tournaments
        roomMode: RoomMode.RoomModeNone, // TODO: or RoomMode.RoomModeBomb
        currency: CurrencyType.DIAMOND, // this is probably invalid and users don't use currency at all. probably depends on room.useWallet
        blinds,
        straddle: room.isThirdBlind ? true : false,
        ante: room.gradeCfg.ante as number,
        playersCount,
        maxPlayers: room.gamePersonNum as number,
        leftSeats: +room.gamePersonNum - playersCount,
        tableName: room.title as string,
        appId: AppType.FRIENDS,
    };
}

async function joinRoomAndPlay(user: PlatformUserData, roomId, websocketBaseUrl, seatNum?: number): Promise<void> {
    console.log(`Joining room ${roomId} for user ${user.userId}`, { user });
    GameLogic.setUserStatus(UserStatus.inLobby);
    const roomInfo = await HttpApi.checkUserIntoRoomByRoomId(roomId);
    console.log(`Checked into room ${roomId}, joining`, roomInfo);
    const encryptedGameAesKey = await HttpApi.generateGameAESKey(roomId);
    await connectToGameWebsocket(
        user,
        websocketBaseUrl,
        roomInfo.urlPath,
        roomInfo.roomId,
        encryptedGameAesKey,
    );

    const room = await sendJoinRoom();
    GameLogic.setUserStatus(UserStatus.inRoom);

    await sleep(1500); // Some wait required between actions

    try {
        const resp = await sitDown(room, seatNum);
        GameLogic.setUserStatus(UserStatus.satDown);
        console.log(`Successfully joined room ${roomId} with seat number ${resp.optSeatNum}`);
    } catch (error) {
        if (error && error.errorCode === 7007) {
            GameLogic.setUserStatus(PlayerStatus.FAILED);
            await exitRoom();
        }
        console.error('Error joining room', error);
    }

    return onExitedRoomPromise;
}

async function sitDown(room: BattleRoomMsg, seatNum: number | undefined): Promise<BattleRoomMsg> {
    if (!room.gamePersonNum) {
        throw new GameError('Room does not have gamePersonNum defined');
    }
    // using room.optSeatNum sometimes results with `"errorCode":7007,"errMsg":"Seat is occupied"`
    seatNum = seatNum || (room.optSeatNum as number); // seat recommended by server
    const occupiedSeats = room.sitUserList.map((user) => +user.seatNum);
    if (occupiedSeats.includes(seatNum)) {
        seatNum = undefined; // if seat is occupied, we will find free seat below
    }
    if (!seatNum) {
        const freeSeats: number[] = [];

        // seatNum starts from 1
        for (let i = 1; i <= (room.gamePersonNum as number); i++) {
            if (!occupiedSeats.includes(i)) {
                freeSeats.push(i);
            }
        }
        seatNum = freeSeats.at(Math.floor(Math.random() * freeSeats.length));
    }

    console.log(`Requesting seat ${seatNum} in room ${room.roomId}`);
    return sendRequestSeat(seatNum);
}

/*
roomList does not contain clubId, but contains all rooms from all clubs and non-clubs for player,
so we need to filter it by clubName
 */
async function scanPlayerTables(club_id: number, jobDataHandler: JobDataHandler): Promise<void> {
    const clubs = await HttpApi.getPlayerClubs();
    console.log('Player clubs:', clubs);
    const currentClub = clubs.find((club) => club.clubId == club_id);

    if (!currentClub) {
        console.error('Current club not found', null, { clubs, club_id });
        throw new Error(`Club with id ${club_id} not found`);
    }

    while(true) {
        const roomsRecord = await HttpApi.getRoomsCreatedByUser();
        console.log('Player rooms', { rooms: roomsRecord.roomList });
        const tables = roomsRecord.roomList
            .filter((room) => room.clubName == currentClub.clubName && +room.playType == PlayType.NLHE)
            .map(toTableData);
        jobDataHandler({ tables, club_id });

        await sleep(10_000);
    }
}
