import dotenv from 'dotenv';
import fs from 'fs';
dotenv.config({ path: '../../.env.local', override: true });
if (fs.existsSync('../../private.env')) {
    dotenv.config({ path: `../../private.env`, override: true });
}

import {
    createRoom,
    CreateRoomParams,
    // getBalanceDiamond,
    getRoomsCreatedByUser,
    setCurrentUser,
    getPlayerClubs,
    wpkHttpRequest,
    // getUserWalletInfo,
} from './http_api';
import { FriendsMain } from './index';
import { RobotHttpReq } from 'pkw';

/**
 * # Options to run this script
 * - npm start // will use user 0 and create a room if not existing
 * - npm start 1 // will use user 1 and create a room if not existing
 * - npm start 2 123456 // will use user 2 and join room with id 123456
 */

const wpkHttpURL = process.env.WPK_URL || "http://**************/wepoker";
const DEV_URL_CONFIG = {
    wpkHttpURL,
    wpkGameWsURL: wpkHttpURL.replace('http', 'ws'), // tmp for development
};

// const clubId = 6889581;


export class User {
    userId: string;
    username: string;
    token: string;
    aesKey?: string;
    gameAesKey?: string;
    password: string;
    deviceId: string;
	countryCode?: string;
    platformId: number;

    constructor(userId, username, password) {
        this.userId = userId;
        this.username = username;
        this.password = password;
    }
}

// users from CSV, they use the same passoword
const test_users = [
    new User(34579581, "a5team018", "a5team@018"), // club id 6889581
    new User(34579573, "a5team010", "a5team@010"),
    new User(34579569, "a5team007", "a5team@007"),
    new User(34574158, "ut00004974", "03F869A5FE21C5F8CA684212F7950514"),
    new User(34574159, "ut00004975", "03F869A5FE21C5F8CA684212F7950514"),
    new User(34574160, "ut00004976", "03F869A5FE21C5F8CA684212F7950514"),
    new User(34574161, "ut00004977", "03F869A5FE21C5F8CA684212F7950514"),
];

const user = test_users[process.argv[2] || 0];

(async () => {
    FriendsMain.init(DEV_URL_CONFIG)
    const response = await RobotHttpReq.wpkLogin(user as unknown as any);
    console.log('Login response', response);
    const { sessionToken } = response;
    user.token = sessionToken;
    console.log('Login successful');
    setCurrentUser(user);
    // const balance = await getBalanceDiamond();
    // console.log({ balance })

    // const userWalletInfo = await getUserWalletInfo();
    // console.log({ userWalletInfo })

    let roomId: number;
    if (process.argv[3]) {
        roomId = +process.argv[3];

    } else {

        const roomRecord = await getRoomsCreatedByUser();
        console.log('Rooms created by user', roomRecord);
        let room = roomRecord.roomList[0];

        const club = await wpkHttpRequest('/getUserClubNum')
        console.log('Player clubs count', club);

        const clubs = await getPlayerClubs()
        console.log('Player clubs', clubs)

        return;

        if (!room) {
            const ROOM_CONFIG_PLAY_TYPE = 0; // select any or pass from outside, it is one of bot start params

            const ROOM_PLAY_TYPES = [
                /* short with ante */ { "playType": 0, "duration": 30, "isNeedAgreeSit": 0, "grade": 1, "maxScoreMultiple": -1, "minScoreMultiple": 1, "minBuyInScore": 200, "maxBuyInScore": -1, "isAnte": 1, "ante": 1, "gameFund": 0, "gameFundRate": 0.0, "isSendDouble": 0, "roomType": 1, "isLimitIp": 0, "gamePersonNum": 8, "isLimitScore": 0, "isThirdBlind": 1, "isActSeeCard": 0, "isRandSeat": 0, "useWallet": 0, clubId: 0, isDoubleAnte: 0, shortCompareType: 0, forceLive: 0, allowLive: 0 },
                /* long without ante */ { "playType": 0, "duration": 480, "isNeedAgreeSit": 0, "grade": 1, "maxScoreMultiple": -1, "minScoreMultiple": 1, "minBuyInScore": 200, "maxBuyInScore": -1, "isAnte": 0, "ante": 0, "gameFund": 0, "gameFundRate": 0.0, "isSendDouble": 0, "roomType": 1, "isLimitIp": 0, "gamePersonNum": 9, "isLimitScore": 0, "isThirdBlind": 0, "isActSeeCard": 0, "isRandSeat": 0 },
                /* shortdeck */ { "playType": 1, "ante": 2, "duration": 480, "gameFund": 0, "gameFundRate": 0.0, "gamePersonNum": 9, "grade": 2, "isActSeeCard": 0, "isAnte": 0, "isLimitIp": 0, "isLimitScore": 0, "isNeedAgreeSit": 0, "isRandSeat": 0, "isSendDouble": 0, "isThirdBlind": 0, "maxBuyInScore": -1, "maxScoreMultiple": -1, "minBuyInScore": 100, "minScoreMultiple": 1, "roomType": 1, "shortCompareType": 1, "minKeepScore": 0, "maxKeepScore": 0 },
                /* NOT SUPPORTED omaha */ { "playType": 2, "ante": 0, "duration": 480, "gameFund": 0, "gameFundRate": 0.0, "gamePersonNum": 9, "grade": 1, "isActSeeCard": 0, "isAnte": 0, "isLimitIp": 0, "isLimitScore": 1, "isNeedAgreeSit": 0, "isRandSeat": 0, "isSendDouble": 0, "isThirdBlind": 0, "maxBuyInScore": -1, "maxScoreMultiple": -1, "minBuyInScore": 200, "minScoreMultiple": 1, "roomType": 1, }
            ]
            const roomPlayConfig = ROOM_PLAY_TYPES.find(t => t.playType == ROOM_CONFIG_PLAY_TYPE) || ROOM_PLAY_TYPES[0];
            (roomPlayConfig as any).roomTitle = user.userId + " custom room";

            const response = await createRoom(roomPlayConfig as CreateRoomParams);
            if (response.errorCode) {
                throw "cannot create room";
            }
            console.log('Created room');

            const roomRecord = await getRoomsCreatedByUser();
            room = roomRecord.roomList[0];
        }

        console.log(`Using room`, { room });
        roomId = room.roomId as number;
    }

    const onMessage = () => {}
    await FriendsMain.play(user, roomId, onMessage);

})().catch(err => {
    console.error(err);
    process.exit(1);
});

